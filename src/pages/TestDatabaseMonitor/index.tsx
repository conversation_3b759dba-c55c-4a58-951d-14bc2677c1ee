import RkArea from '@/components/Charts/Area';
import RkBar from '@/components/Charts/Bar';
import RkLine from '@/components/Charts/Line';
import { PageContainer, ProCard, ProTable } from '@ant-design/pro-components';
import { Col, Row } from 'antd';
import React, { useState } from 'react';

const TestDatabaseMonitor: React.FC = () => {
  const [loading] = useState(false);

  // 模拟数据
  const asmDiskData = [
    { time: '00:00', value: 65, category: 'DATA' },
    { time: '01:00', value: 67, category: 'DATA' },
    { time: '02:00', value: 64, category: 'DATA' },
    { time: '03:00', value: 66, category: 'DATA' },
    { time: '04:00', value: 65, category: 'DATA' },
    { time: '05:00', value: 68, category: 'DATA' },
    { time: '06:00', value: 67, category: 'DATA' },
    { time: '07:00', value: 69, category: 'DATA' },
    { time: '08:00', value: 68, category: 'DATA' },
    { time: '09:00', value: 70, category: 'DATA' },
    { time: '10:00', value: 69, category: 'DATA' },
    { time: '11:00', value: 71, category: 'DATA' },
    { time: '12:00', value: 70, category: 'DATA' },
    { time: '00:00', value: 55, category: 'FRA' },
    { time: '01:00', value: 57, category: 'FRA' },
    { time: '02:00', value: 54, category: 'FRA' },
    { time: '03:00', value: 56, category: 'FRA' },
    { time: '04:00', value: 55, category: 'FRA' },
    { time: '05:00', value: 58, category: 'FRA' },
    { time: '06:00', value: 57, category: 'FRA' },
    { time: '07:00', value: 59, category: 'FRA' },
    { time: '08:00', value: 58, category: 'FRA' },
    { time: '09:00', value: 60, category: 'FRA' },
    { time: '10:00', value: 59, category: 'FRA' },
    { time: '11:00', value: 61, category: 'FRA' },
    { time: '12:00', value: 60, category: 'FRA' },
    { time: '00:00', value: 45, category: 'OCR' },
    { time: '01:00', value: 47, category: 'OCR' },
    { time: '02:00', value: 44, category: 'OCR' },
    { time: '03:00', value: 46, category: 'OCR' },
    { time: '04:00', value: 45, category: 'OCR' },
    { time: '05:00', value: 48, category: 'OCR' },
    { time: '06:00', value: 47, category: 'OCR' },
    { time: '07:00', value: 49, category: 'OCR' },
    { time: '08:00', value: 48, category: 'OCR' },
    { time: '09:00', value: 50, category: 'OCR' },
    { time: '10:00', value: 49, category: 'OCR' },
    { time: '11:00', value: 51, category: 'OCR' },
    { time: '12:00', value: 50, category: 'OCR' },
  ];

  const activeConnectionData = [
    { time: '00:00', value: 50, category: '会话数' },
    { time: '01:00', value: 52, category: '会话数' },
    { time: '02:00', value: 48, category: '会话数' },
    { time: '03:00', value: 55, category: '会话数' },
    { time: '04:00', value: 60, category: '会话数' },
    { time: '05:00', value: 58, category: '会话数' },
    { time: '06:00', value: 62, category: '会话数' },
    { time: '07:00', value: 65, category: '会话数' },
    { time: '08:00', value: 68, category: '会话数' },
    { time: '09:00', value: 70, category: '会话数' },
    { time: '10:00', value: 72, category: '会话数' },
    { time: '11:00', value: 68, category: '会话数' },
    { time: '12:00', value: 65, category: '会话数' },
    { time: '00:00', value: 45, category: '活跃连接' },
    { time: '01:00', value: 47, category: '活跃连接' },
    { time: '02:00', value: 43, category: '活跃连接' },
    { time: '03:00', value: 50, category: '活跃连接' },
    { time: '04:00', value: 55, category: '活跃连接' },
    { time: '05:00', value: 53, category: '活跃连接' },
    { time: '06:00', value: 57, category: '活跃连接' },
    { time: '07:00', value: 60, category: '活跃连接' },
    { time: '08:00', value: 63, category: '活跃连接' },
    { time: '09:00', value: 65, category: '活跃连接' },
    { time: '10:00', value: 67, category: '活跃连接' },
    { time: '11:00', value: 63, category: '活跃连接' },
    { time: '12:00', value: 60, category: '活跃连接' },
  ];

  const eventWaitData = [
    { category: 'buffer_busy_waits', value: 3 },
    { category: 'db_file_scattered_read', value: 3 },
    { category: 'db_file_sequential_read', value: 2 },
    { category: 'direct_path_read', value: 2 },
    { category: 'enq_TX_allocate_ITL_entry', value: 2 },
    { category: 'enq_TX_contention', value: 2 },
    { category: 'enq_TX_index_contention', value: 0 },
    { category: 'enq_TX_row_lock_contention', value: 1 },
    { category: 'log_file_switch_archiving_need...', value: 2 },
    { category: 'log_file_switch_checkpoint_inc...', value: 2 },
  ];

  const tablespaceData = [
    {
      key: '1',
      name: 'NNC_DATA01',
      usage: '87.77%',
      used: '337 GB',
      total: '340 GB',
      maxSize: '344 GB',
    },
    {
      key: '2',
      name: 'SYSTEM',
      usage: '6.86%',
      used: '2.36 GB',
      total: '25.0 GB',
      maxSize: '34.4 GB',
    },
    {
      key: '3',
      name: 'SYSAUX',
      usage: '49.86%',
      used: '51.4 GB',
      total: '57.8 GB',
      maxSize: '103 GB',
    },
    {
      key: '4',
      name: 'UNDOTBS2',
      usage: '1.74%',
      used: '599 MB',
      total: '13.2 GB',
      maxSize: '34.4 GB',
    },
    {
      key: '5',
      name: 'UNDOTBS1',
      usage: '1.18%',
      used: '407 MB',
      total: '6.17 GB',
      maxSize: '34.4 GB',
    },
    {
      key: '6',
      name: 'JQ_DATAENGINE',
      usage: '0.00%',
      used: '1.05 MB',
      total: '1.05 GB',
      maxSize: '34.4 GB',
    },
    {
      key: '7',
      name: 'USERS',
      usage: '0.12%',
      used: '41.2 MB',
      total: '44.6 MB',
      maxSize: '34.4 GB',
    },
  ];

  const tablespaceColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '使用率',
      dataIndex: 'usage',
      key: 'usage',
      render: (text: any) => {
        const percentage = parseFloat(text);
        let color = '#52c41a'; // 绿色
        if (percentage > 80) color = 'red'; // 红色
        return <span style={{ color, fontWeight: 'bold' }}>{text}</span>;
      },
    },
    {
      title: '当前使用量',
      dataIndex: 'used',
      key: 'used',
    },
    {
      title: '表空间总量',
      dataIndex: 'total',
      key: 'total',
    },
    {
      title: '自动扩展上限',
      dataIndex: 'maxSize',
      key: 'maxSize',
    },
  ];

  return (
    <PageContainer header={{ title: '数据库监控测试页面' }}>
      {/* 顶部状态卡片 */}
      <Row gutter={16}>
        <Col span={6}>
          <ProCard
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
            }}
            title="状态"
            loading={loading}
            bodyStyle={{
              height: '90px',
              padding: '16px',
              textAlign: 'center',
            }}
          >
            <div style={{ fontSize: '24px', color: '#52c41a', fontWeight: 'bold' }}>正常</div>
          </ProCard>
        </Col>
        <Col span={6}>
          <ProCard
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
            }}
            title="监听运行节点数"
            loading={loading}
            bodyStyle={{
              height: '90px',
              padding: '16px',
              textAlign: 'center',
            }}
          >
            <div style={{ fontSize: '24px', fontWeight: 'bold' }}>1</div>
          </ProCard>
        </Col>
        <Col span={6}>
          <ProCard
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
            }}
            title="活动会话数"
            loading={loading}
            bodyStyle={{
              height: '90px',
              padding: '16px',
              textAlign: 'center',
            }}
          >
            <div style={{ fontSize: '24px', fontWeight: 'bold' }}>2</div>
          </ProCard>
        </Col>
        <Col span={6}>
          <ProCard
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
            }}
            title="dg日志延迟个数"
            loading={loading}
            bodyStyle={{
              height: '90px',
              padding: '16px',
              textAlign: 'center',
            }}
          >
            <div style={{ fontSize: '24px', fontWeight: 'bold' }}>0</div>
          </ProCard>
        </Col>
      </Row>

      {/* 中间图表区域 */}
      <Row gutter={16}>
        <Col span={12}>
          <ProCard
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
            }}
            title="asm磁盘组使用率"
            loading={loading}
            bodyStyle={{
              padding: '16px',
            }}
          >
            <RkLine
              data={asmDiskData}
              xField="time"
              yField="value"
              seriesField="category"
              height={200}
              color={['#1890ff', '#52c41a', '#faad14']}
              legend={{
                position: 'top',
                marker: {
                  symbol: (x: number, y: number) => {
                    // 创建线段+空心圆的组合标记
                    return [
                      // 绘制线段
                      ['M', x - 8, y],
                      ['L', x + 8, y],
                      // 移动到圆心并绘制空心圆
                      ['M', x + 3, y],
                      ['A', 3, 3, 0, 1, 1, x - 3, y],
                      ['A', 3, 3, 0, 1, 1, x + 3, y],
                    ];
                  },
                  style: {
                    fill: 'white',
                    lineWidth: 2,
                  },
                },
              }}
              smooth
              // point={{
              //   size: 4,
              //   shape: 'circle',
              //   style: (datum: any) => {
              //     const colors = ['#1890ff', '#52c41a', '#faad14'];
              //     const categories = ['DATA', 'FRA', 'OCR'];
              //     const colorIndex = categories.indexOf(datum.category);
              //     const strokeColor = colorIndex >= 0 ? colors[colorIndex] : '#1890ff';

              //     return {
              //       fill: 'white',
              //       stroke: strokeColor,
              //       lineWidth: 2,
              //     };
              //   },
              // }}
              point={{
                shape: 'dot',
                size: 3,
              }}
              yAxis={{
                min: 0,
                max: 100,
                label: {
                  formatter: (v: string) => `${v}%`,
                },
              }}
            />
          </ProCard>
        </Col>
        <Col span={12}>
          <ProCard
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
            }}
            title="活动连接数"
            loading={loading}
            bodyStyle={{
              padding: '16px',
            }}
          >
            <RkArea
              data={activeConnectionData}
              xField="time"
              yField="value"
              seriesField="category"
              height={200}
              color={['#722ed1', '#13c2c2']}
              smooth
              legend={{
                position: 'top',
              }}
              yAxis={{
                grid: {
                  line: { style: { stroke: '#f0f0f0' } },
                },
              }}
              slider={false}
              scrollbar={false}
            />
          </ProCard>
        </Col>
      </Row>

      {/* 底部区域 */}
      <Row gutter={16}>
        <Col span={12}>
          <ProCard
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
              minHeight: 420,
            }}
            title="重要事件等待个数"
            loading={loading}
            bodyStyle={{
              padding: '16px',
            }}
          >
            <RkBar
              data={eventWaitData}
              xField="value"
              yField="category"
              height={340}
              color="rgb(198,241,239)"
              padding={[20, 40, 20, 200]}
              label={{
                position: 'right',
                offset: 10,
                style: {
                  fill: '#000',
                  fontSize: 12,
                },
                formatter: (datum: any) => {
                  return datum.value.toString();
                },
              }}
              xAxis={{
                grid: {
                  line: {
                    style: {
                      stroke: 'transparent',
                    },
                  },
                },
                label: {
                  style: {
                    opacity: 0,
                  },
                },
                line: {
                  style: {
                    stroke: 'transparent',
                  },
                },
                tickLine: {
                  style: {
                    stroke: 'transparent',
                  },
                },
              }}
              yAxis={{
                line: {
                  style: {
                    stroke: '#f0f0f0',
                  },
                },
                tickLine: {
                  style: {
                    stroke: 'transparent',
                  },
                },
              }}
            />
          </ProCard>
        </Col>
        <Col span={12}>
          <ProCard
            style={{
              backgroundColor: '#fff',
              marginBlockStart: 16,
              minHeight: 420,
            }}
            title="表空间使用情况"
            loading={loading}
            bodyStyle={{
              padding: '16px',
            }}
          >
            <ProTable
              columns={tablespaceColumns}
              dataSource={tablespaceData}
              search={false}
              options={false}
              pagination={false}
              size="small"
              scroll={{ y: 300 }}
            />
          </ProCard>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default TestDatabaseMonitor;
